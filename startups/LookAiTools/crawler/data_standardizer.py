#!/usr/bin/env python3
"""
AI工具数据标准化处理脚本
使用AWS Bedrock Claude将爬取的原始数据标准化为数据库格式
"""

import json
import os
import re
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import boto3
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class AWSBedrockStandardizer:
    def __init__(self):
        """初始化AWS Bedrock客户端"""
        self.bedrock = boto3.client(
            'bedrock-runtime',
            region_name=os.getenv('AWS_REGION', 'us-east-1'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
        )
        self.model_id = "anthropic.claude-3-5-sonnet-20241022-v2:0"
        

    
    def extract_headings_text(self, headings: list) -> str:
        """提取标题文本"""
        if not headings:
            return ""

        heading_texts = []
        for heading in headings:
            level = heading.get('level', 1)
            text = heading.get('text', '')
            if text:
                heading_texts.append(f"H{level}: {text}")

        return "\n".join(heading_texts)

    def format_input_data(self, raw_data: Dict[str, Any]) -> str:
        """格式化输入数据为结构化文本 - 提取所有可用字段"""

        # 提取基础信息
        product_info = raw_data.get('product_info', {})
        landpage = raw_data.get('landpage', {})

        # 基础字段
        name = raw_data.get('name', '')
        website_url = raw_data.get('website_url', '')
        crawl_time_str = raw_data.get('crawl_time_str', '')

        # product_info 所有字段
        description = product_info.get('description', '')
        category = product_info.get('category', '')
        tags = product_info.get('tags', [])
        pricing = product_info.get('pricing', '')
        features = product_info.get('features', [])
        rating = product_info.get('rating', '')
        reviews = product_info.get('reviews', '')
        company = product_info.get('company', '')
        launch_date = product_info.get('launch_date', '')
        social_links = product_info.get('social_links', {})

        # landpage 所有字段
        page_screenshot = landpage.get('page_screenshot', '')
        full_page_text = landpage.get('full_page_text', '')[:4000]  # 增加到4000字符
        page_title = landpage.get('page_title', '')
        headings = landpage.get('headings', [])
        headings_text = self.extract_headings_text(headings)

        # meta_data 详细信息
        meta_data = landpage.get('meta_data', {})
        meta_description = meta_data.get('description', '')
        meta_keywords = meta_data.get('keywords', '')
        meta_author = meta_data.get('author', '')
        meta_title = meta_data.get('title', '')

        # links 信息
        links = landpage.get('links', [])
        # links 是一个数组，包含 url 和 text 字段
        link_urls = [link.get('url', '') for link in links[:10]] if isinstance(links, list) else []

        # images 信息
        images = landpage.get('images', [])

        # forms 信息
        forms = landpage.get('forms', [])

        # scripts 信息
        scripts = landpage.get('scripts', [])

        # 格式化输入数据
        formatted_data = f"""=== 基础信息 ===
name: {name}
website_url: {website_url}
crawl_time_str: {crawl_time_str}

=== 产品信息 (product_info) ===
product_info.description: {description}
product_info.category: {category}
product_info.tags: {tags}
product_info.pricing: {pricing}
product_info.features: {features}
product_info.rating: {rating}
product_info.reviews: {reviews}
product_info.company: {company}
product_info.launch_date: {launch_date}
product_info.social_links: {social_links}

=== 网页信息 (landpage) ===
landpage.page_title: {page_title}
landpage.page_screenshot: {page_screenshot}

=== 元数据 (meta_data) ===
landpage.meta_data.title: {meta_title}
landpage.meta_data.description: {meta_description}
landpage.meta_data.keywords: {meta_keywords}
landpage.meta_data.author: {meta_author}

=== 页面结构 ===
landpage.headings:
{headings_text}

landpage.images (前10个): {images[:10]}

landpage.forms: {forms}

=== 链接信息 ===
landpage.links (前10个URL): {link_urls}

=== 脚本信息 ===
landpage.scripts (前3个): {scripts[:3]}

=== 完整页面内容 (前4000字符) ===
landpage.full_page_text:
{full_page_text}"""

        return formatted_data

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """# AI工具信息提取系统提示词

## 任务说明
你是一个专业的AI工具分析师，负责从爬取的网站数据中提取和标准化AI工具信息。请基于提供的完整数据，进行深度分析并输出标准化的JSON格式结果。

## 分析要求

### 1. 数据源优先级
- **优先级1**: full_page_text（网页完整内容）- 包含最详细的功能描述
- **优先级2**: page_title（页面标题）- 核心产品定位
- **优先级3**: headings（标题结构）- 功能模块划分
- **优先级4**: pricing（定价信息）- 商业模式
- **优先级5**: meta_description（元描述）- 简要概述

### 2. 深度分析标准
- **功能识别**: 从技术实现、应用场景、解决问题三个维度分析核心功能
- **特性提取**: 识别独特卖点、技术优势、用户价值
- **定位判断**: 基于目标用户、使用场景、竞争优势确定工具定位
- **分类逻辑**: 以主要功能为准，兼顾技术实现方式

### 3. 内容质量要求
- **准确性**: 信息必须基于实际网页内容，不得推测或编造
- **专业性**: 使用行业标准术语，避免营销化表述
- **本土化**: 中文表达符合国内用户习惯，避免直译腔
- **完整性**: 尽可能从现有信息中提取完整的工具画像

## 输出JSON规范

```json
{
    "name": {
        "cn": "工具中文名称（如果有中文名称就用中文，没有就用英文，英文需要用大写）",
        "en": "工具英文名称（大写，如 CHATGPT"
    },
    "title": {
        "cn": "工具中文显示名称（适合展示的完整名称）",
        "en": "工具英文显示名称（官方完整英文名称）"
    },
    "slug": "URL友好标识符（小写，连字符分隔，与name.en一致）",
    "url": "官方网站完整链接（包含https://）",
    "page_screenshot": "工具官网截图路径（从landpage.page_screenshot字段获取）",
    "description": {
        "cn": "中文核心功能描述（80-120字，突出主要价值）",
        "en": "英文核心功能描述（80-120字，突出主要价值）"
    },
    "long_description": {
        "cn": "中文详细介绍（250-350字，包含技术特点、应用场景、核心优势）",
        "en": "英文详细介绍（250-350字，包含技术特点、应用场景、核心优势）"
    },  
    "key_features": [
        {"cn": "核心特性中文描述（15-25字）", "en": "Core feature English description (10-20 words)"},
        {"cn": "另一特性中文描述", "en": "Another feature English description"}
    ],
    "use_cases": {
        "cn": "主要应用场景（具体化，如：内容创作者制作短视频、企业客服自动化等）",
        "en": "Main use cases (specific scenarios, e.g., content creators making short videos, enterprise customer service automation)"
    },
    "target_audience": {
        "cn": "目标用户群体（细分化，如：中小企业营销团队、个人内容创作者等）", 
        "en": "Target audience (segmented user groups, e.g., SME marketing teams, individual content creators)"
    },
    "category": "主分类（必须从指定列表选择最符合核心功能的分类）",
    "subcategory": {
        "cn": "子分类中文名称（可选，更具体的功能分类）",
        "en": "Subcategory English name (optional, more specific functional classification)"
    },
    "tags": [
        {"cn": "功能标签", "en": "functional tag"},
        {"cn": "技术标签", "en": "technical tag"},
        {"cn": "应用标签", "en": "application tag"}
    ],
    "industry_tags": [
        {"cn": "适用行业1", "en": "applicable industry 1"},
        {"cn": "适用行业2", "en": "applicable industry 2"}
    ],
    "pricing_type": {
        "cn": "定价模式描述（免费/免费增值/付费/企业版/未知）",
        "en": "Pricing model description (free/freemium/paid/enterprise/unknown)"
    },
    "pricing_details": {
        "free_plan": {
            "cn": "免费版功能描述（如有）",
            "en": "Free plan description (if available)"
        },
        "paid_plans": [
            {
                "name": {"cn": "付费版本中文名", "en": "Paid plan English name"},
                "price": "具体价格（包含币种和周期）",
                "features": [
                    {"cn": "付费功能中文说明", "en": "Paid feature English description"}
                ]
            }
        ]
    },
    "trial_available": {
        "cn": "是否提供试用（是/否/未知）",
        "en": "Trial availability (yes/no/unknown)"
    },
    "rating": 0,
    "view_count": 0, 
    "traffic_estimate": 0,
    "featured": false,
    "status": "active"
}
```

## 分类标准（category字段）
- **image**: 图像生成、编辑、处理
- **video**: 视频创作、编辑、处理  
- **productivity**: 效率工具、自动化、工作流
- **text&writing**: 文本生成、写作辅助、内容创作
- **business**: 商业分析、管理工具、企业应用
- **chatbot**: 对话机器人、客服系统
- **design&art**: 设计工具、艺术创作
- **code&it**: 编程辅助、开发工具、技术应用
- **marketing**: 营销工具、推广、广告
- **prompt**: 提示词工程、AI交互优化
- **voice**: 语音合成、识别、处理
- **ai tools directory**: AI工具导航、聚合平台
- **ai detector**: AI内容检测、识别
- **life assistant**: 生活助手、个人服务
- **3d**: 3D建模、渲染、设计
- **education**: 教育培训、学习辅助
- **other**: 其他未分类工具

## 特殊处理规则
1. **多功能工具**: 按主要功能分类，次要功能体现在subcategory和tags中
2. **信息缺失**: 无法确定的字段使用null，不要编造信息
3. **定价复杂**: 尽量提取核心定价信息，过于复杂的定价可简化描述
4. **英文名称**: 确保name.en是有效的英文标识符，避免特殊字符
5. **标签去重**: tags和industry_tags避免重复，保持简洁有效
6. **中英文对应**: 所有支持中英文的字段必须提供对应翻译，确保语义一致
7. **数据类型**: pricing_type和trial_available字段仍使用简单字符串，其他需要中英文的字段使用对象格式

## 输出要求
- **纯JSON格式**: 不包含任何解释文字或markdown标记
- **格式正确**: 确保JSON语法正确，可被程序解析
- **编码统一**: 使用UTF-8编码，正确处理中英文字符
- **字段完整**: 所有必需字段都要包含，可选字段根据实际情况填写

请基于以上标准，深入分析提供的AI工具数据，输出标准化的JSON结果。"""

    def get_user_prompt(self, formatted_data: str) -> str:
        """获取用户提示词"""
        return f"""网页爬取信息的输入数据如下：
```
{formatted_data}
```
"""


    def call_bedrock_claude(self, system_prompt: str, user_prompt: str) -> Optional[str]:
        """调用AWS Bedrock Claude API"""
        try:
            body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 3000,
                "temperature": 0.1,
                "system": system_prompt,
                "messages": [
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            }

            response = self.bedrock.invoke_model(
                modelId=self.model_id,
                body=json.dumps(body)
            )

            response_body = json.loads(response['body'].read())
            return response_body['content'][0]['text']

        except Exception as e:
            print(f"调用Bedrock失败: {e}")
            return None
    
    def clean_json_response(self, response: str) -> str:
        """清理JSON响应"""
        if not response:
            return ""
        
        # 移除可能的markdown格式
        response = response.strip()
        if response.startswith('```json'):
            response = response[7:]
        if response.startswith('```'):
            response = response[3:]
        if response.endswith('```'):
            response = response[:-3]
        
        return response.strip()
    
    def generate_slug(self, name: str) -> str:
        """生成URL友好的slug"""
        slug = name.lower()
        slug = re.sub(r'[^a-z0-9\s-]', '', slug)
        slug = re.sub(r'\s+', '-', slug)
        slug = slug.strip('-')
        return slug
    
    def validate_and_clean_data(self, data: Dict[str, Any], raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理标准化后的数据"""

        # 处理新的双语结构
        def extract_bilingual_field(field_data, fallback_en='', fallback_cn=''):
            if isinstance(field_data, dict):
                return {
                    'en': field_data.get('en', fallback_en),
                    'cn': field_data.get('cn', fallback_cn)
                }
            elif isinstance(field_data, str):
                return {'en': field_data, 'cn': field_data}
            else:
                return {'en': fallback_en, 'cn': fallback_cn}

        # 必需字段检查和转换
        raw_name = raw_data.get('name', 'Unknown Tool')

        # 处理name字段
        if not data.get('name'):
            data['name'] = {'en': self.generate_slug(raw_name), 'cn': raw_name}
        else:
            data['name'] = extract_bilingual_field(data['name'], self.generate_slug(raw_name), raw_name)

        # 处理title字段
        if not data.get('title'):
            data['title'] = {'en': raw_name, 'cn': raw_name}
        else:
            data['title'] = extract_bilingual_field(data['title'], raw_name, raw_name)

        # 处理slug字段
        if not data.get('slug'):
            name_en = data['name'].get('en', raw_name) if isinstance(data['name'], dict) else raw_name
            data['slug'] = self.generate_slug(name_en)

        # 处理url字段
        if not data.get('url'):
            data['url'] = raw_data.get('website_url', '')

        # 处理page_screenshot字段
        if not data.get('page_screenshot'):
            landpage = raw_data.get('landpage', {})
            data['page_screenshot'] = landpage.get('page_screenshot', '')

        # 处理description字段
        if data.get('description'):
            data['description'] = extract_bilingual_field(data['description'])

        # 处理long_description字段
        if data.get('long_description'):
            data['long_description'] = extract_bilingual_field(data['long_description'])

        # 处理use_cases字段
        if data.get('use_cases'):
            data['use_cases'] = extract_bilingual_field(data['use_cases'])

        # 处理target_audience字段
        if data.get('target_audience'):
            data['target_audience'] = extract_bilingual_field(data['target_audience'])

        # 处理subcategory字段（新增的双语字段）
        if data.get('subcategory'):
            data['subcategory'] = extract_bilingual_field(data['subcategory'])

        # 处理tags字段（现在是双语数组）
        if data.get('tags'):
            if isinstance(data['tags'], list):
                processed_tags = []
                for tag in data['tags']:
                    if isinstance(tag, dict) and 'cn' in tag and 'en' in tag:
                        processed_tags.append(tag)
                    elif isinstance(tag, str):
                        processed_tags.append({'cn': tag, 'en': tag})
                data['tags'] = processed_tags
        else:
            data['tags'] = []

        # 处理industry_tags字段（双语数组）
        if data.get('industry_tags'):
            if isinstance(data['industry_tags'], list):
                processed_industry_tags = []
                for tag in data['industry_tags']:
                    if isinstance(tag, dict) and 'cn' in tag and 'en' in tag:
                        processed_industry_tags.append(tag)
                    elif isinstance(tag, str):
                        processed_industry_tags.append({'cn': tag, 'en': tag})
                data['industry_tags'] = processed_industry_tags
        else:
            data['industry_tags'] = []

        # 处理pricing_type字段（现在是双语对象）
        if data.get('pricing_type'):
            data['pricing_type'] = extract_bilingual_field(data['pricing_type'])

        # 处理trial_available字段（现在是双语对象）
        if data.get('trial_available'):
            data['trial_available'] = extract_bilingual_field(data['trial_available'])

        # 分类验证
        valid_categories = [
            'image', 'video', 'productivity', 'text&writing', 'business',
            'chatbot', 'design&art', 'code&it', 'marketing', 'prompt',
            'voice', 'ai tools directory', 'ai detector', 'life assistant',
            '3d', 'education', 'other'
        ]
        if data.get('category') not in valid_categories:
            # 如果分类无效，设置为默认值
            data['category'] = 'other'

        # 数值字段验证
        for field in ['rating', 'view_count', 'traffic_estimate']:
            if field in data:
                try:
                    data[field] = float(data[field]) if data[field] else 0
                except (ValueError, TypeError):
                    data[field] = 0

        # 布尔字段验证
        if 'featured' in data:
            data['featured'] = bool(data['featured']) if data['featured'] is not None else False

        # 添加元信息 - 只保存输入模型的格式化数据
        formatted_input = self.format_input_data(raw_data)
        data['meta_info'] = {
            'source_site': 'toolify',
            'crawl_timestamp': raw_data.get('crawl_time_str'),
            'processing_timestamp': datetime.now().isoformat(),
            'model_input_data': formatted_input
        }

        return data
    
    def create_fallback_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建备用数据（当LLM处理失败时）"""
        product_info = raw_data.get('product_info', {})
        name = raw_data.get('name', 'Unknown Tool')

        # 简单的定价类型判断
        pricing_text = product_info.get('pricing', '').lower()
        if 'free' in pricing_text and '$0' in pricing_text:
            pricing_type = 'free'
        elif 'free' in pricing_text or 'trial' in pricing_text:
            pricing_type = 'freemium'
        elif '$' in pricing_text or 'paid' in pricing_text:
            pricing_type = 'paid'
        else:
            pricing_type = 'unknown'

        # 默认分类
        category = 'other'

        description = product_info.get('description', '')[:200]

        # 处理原始标签为双语格式
        raw_tags = product_info.get('tags', [])[:5]
        processed_tags = []
        for tag in raw_tags:
            if isinstance(tag, str):
                processed_tags.append({'cn': tag, 'en': tag})
            elif isinstance(tag, dict):
                processed_tags.append(tag)

        return {
            'name': {'en': self.generate_slug(name), 'cn': name},
            'title': {'en': name, 'cn': name},
            'slug': self.generate_slug(name),
            'url': raw_data.get('website_url', ''),
            'page_screenshot': raw_data.get('landpage', {}).get('page_screenshot', ''),
            'description': {'en': description, 'cn': description},
            'long_description': {'en': product_info.get('description', ''), 'cn': product_info.get('description', '')},
            'key_features': [],
            'use_cases': {'en': '', 'cn': ''},
            'target_audience': {'en': '', 'cn': ''},
            'category': category,
            'subcategory': {'en': '', 'cn': ''},
            'tags': processed_tags,
            'industry_tags': [],
            'pricing_type': {'en': pricing_type, 'cn': pricing_type},
            'pricing_details': {},
            'trial_available': {'en': 'yes' if 'trial' in pricing_text else 'no', 'cn': '是' if 'trial' in pricing_text else '否'},
            'rating': 0,
            'view_count': 0,
            'traffic_estimate': 0,
            'featured': False,
            'status': 'pending',  # 标记为需要人工审核
            'meta_info': {
                'source_site': 'toolify',
                'crawl_timestamp': raw_data.get('crawl_time_str'),
                'processing_timestamp': datetime.now().isoformat(),
                'model_input_data': self.format_input_data(raw_data),
                'fallback_used': True
            }
        }
    
    def standardize_tool_data(self, raw_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """标准化单个工具数据"""
        try:
            # 格式化输入数据
            formatted_data = self.format_input_data(raw_data)

            # 获取系统和用户提示词
            system_prompt = self.get_system_prompt()
            user_prompt = self.get_user_prompt(formatted_data)

            # 调用LLM
            response = self.call_bedrock_claude(system_prompt, user_prompt)

            if not response:
                print(f"LLM调用失败，使用备用数据: {raw_data.get('name', 'Unknown')}")
                return self.create_fallback_data(raw_data)

            # 清理和解析JSON
            cleaned_response = self.clean_json_response(response)
            try:
                standardized_data = json.loads(cleaned_response)
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}, 使用备用数据")
                return self.create_fallback_data(raw_data)

            # 验证和清理数据
            return self.validate_and_clean_data(standardized_data, raw_data)

        except Exception as e:
            print(f"标准化失败: {e}, 使用备用数据")
            return self.create_fallback_data(raw_data)


def load_raw_data(file_path: str) -> Dict[str, Any]:
    """加载原始爬取数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载数据失败: {e}")
        return {}


def save_standardized_data(data: List[Dict[str, Any]], output_path: str):
    """保存标准化数据"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {output_path}")
    except Exception as e:
        print(f"保存数据失败: {e}")


def main():
    """主函数"""
    print("开始数据标准化处理...")
    
    # 初始化标准化器
    standardizer = AWSBedrockStandardizer()
    
    # 输入和输出路径
    input_file = "datas/toolify/toolify_complete_1753335980.json"
    output_dir = "tools_info/toolify"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载原始数据
    print(f"加载原始数据: {input_file}")
    raw_data = load_raw_data(input_file)
    
    if not raw_data or 'tools' not in raw_data:
        print("未找到有效的工具数据")
        return
    
    tools = raw_data['tools']
    total_tools = len(tools)
    print(f"找到 {total_tools} 个工具")
    
    # 处理工具数据
    standardized_tools = []
    processed_count = 0
    error_count = 0
    
    for url, tool_data in tools.items():
        try:
            print(f"处理 {processed_count + 1}/{total_tools}: {tool_data.get('name', 'Unknown')}")
            
            # 标准化数据
            standardized = standardizer.standardize_tool_data(tool_data)
            
            if standardized:
                standardized_tools.append(standardized)
                print(f"✓ 成功处理: {standardized['title']}")
            else:
                error_count += 1
                print(f"✗ 处理失败: {tool_data.get('name', 'Unknown')}")
            
            processed_count += 1
            
            # 控制请求频率，避免API限制
            if processed_count % 10 == 0:
                print(f"已处理 {processed_count} 个工具，暂停2秒...")
                time.sleep(2)
            else:
                time.sleep(0.5)
                
        except Exception as e:
            error_count += 1
            print(f"处理工具时出错: {e}")
            continue
    
    # 保存结果
    if standardized_tools:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{output_dir}/standardized_tools_{timestamp}.json"
        save_standardized_data(standardized_tools, output_file)
        
        # 生成处理报告
        print("\n" + "="*50)
        print("处理完成!")
        print(f"总工具数: {total_tools}")
        print(f"成功处理: {len(standardized_tools)}")
        print(f"处理失败: {error_count}")
        print(f"成功率: {len(standardized_tools)/total_tools*100:.1f}%")
        print(f"输出文件: {output_file}")
        print("="*50)
    else:
        print("没有成功处理任何工具数据")


if __name__ == "__main__":
    main()
