# 数据库表结构设计

## 概述

基于数据收集需求分析，本文档提供了AI工具导航站的完整数据库表结构设计，包括主表、关联表和索引优化方案。

## 设计特点

### 双语支持
- 使用JSONB字段存储中英文内容，格式：`{"en": "English", "cn": "中文"}`
- 支持17个AI工具分类，覆盖图像、视频、文本、商业等各个领域
- 标签和分类均支持双语显示和搜索

### 字段映射
- `name`: 工具名称（双语）
- `title`: 显示标题（双语）
- `description`: 简短描述（双语）
- `page_screenshot`: 官网截图路径（用于前端卡片显示）
- `tags`: 标签数组（双语对象数组）
- `pricing_type`: 定价类型（双语）

### 兼容性
- 保持与现有爬虫输出JSON格式的完全兼容
- 支持从标准化数据直接导入数据库
- 提供完整的索引和查询优化

## 核心表结构

### 1. ai_tools - 工具主表 (支持双语内容)

```sql
CREATE TABLE ai_tools (
    -- 基础标识
    id SERIAL PRIMARY KEY,
    name JSONB NOT NULL,                           -- 工具名称 {"en": "tool-name", "cn": "工具名称"}
    title JSONB NOT NULL,                          -- 显示名称 {"en": "Tool Name", "cn": "工具显示名称"}
    slug VARCHAR(255) UNIQUE NOT NULL,             -- URL友好标识符
    url VARCHAR(500) NOT NULL,                     -- 官方网站
    page_screenshot VARCHAR(500),                  -- 官网截图路径

    -- 描述信息 (双语JSON格式)
    description JSONB NOT NULL,                    -- 简短描述 {"en": "...", "cn": "..."}
    long_description JSONB,                        -- 详细描述 {"en": "...", "cn": "..."}
    key_features JSONB DEFAULT '[]',               -- 核心特性 [{"en": "...", "cn": "..."}]
    use_cases JSONB,                               -- 使用场景 {"en": "...", "cn": "..."}
    target_audience JSONB,                         -- 目标用户群体 {"en": "...", "cn": "..."}

    -- 分类标签
    category VARCHAR(100) NOT NULL,                -- 主要分类 (英文slug)
    subcategory JSONB,                             -- 子分类 {"en": "...", "cn": "..."}
    tags JSONB DEFAULT '[]',                       -- 标签数组 [{"en": "...", "cn": "..."}]
    industry_tags JSONB DEFAULT '[]',              -- 行业标签 [{"en": "...", "cn": "..."}]

    -- 定价信息
    pricing_type JSONB,                            -- 定价类型 {"en": "free", "cn": "免费"}
    pricing_details JSONB DEFAULT '{}',            -- 详细价格信息
    trial_available JSONB,                         -- 是否提供试用 {"en": "yes", "cn": "是"}

    -- 媒体资源 (保持兼容性)
    logo_url VARCHAR(500),                         -- Logo链接
    thumbnail_url VARCHAR(500),                    -- 缩略图链接
    screenshot_urls TEXT[] DEFAULT '{}',           -- 截图链接数组
    video_url VARCHAR(500),                        -- 演示视频
    favicon_url VARCHAR(500),                      -- 网站图标

    -- 统计数据
    rating DECIMAL(3,2) DEFAULT 0,                 -- 评分(0-5)
    view_count INTEGER DEFAULT 0,                  -- 浏览次数
    traffic_estimate BIGINT DEFAULT 0,             -- 流量估算

    -- 状态和元数据
    featured BOOLEAN DEFAULT false,                -- 是否精选
    status VARCHAR(20) DEFAULT 'active',           -- active/pending/rejected

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- 原始信息
    meta_info JSONB DEFAULT '{}',                  -- 存储原始爬取数据和其他元信息

    -- 约束
    CONSTRAINT ai_tools_rating_check CHECK (rating >= 0 AND rating <= 5),
    CONSTRAINT ai_tools_status_check CHECK (status IN ('active', 'pending', 'rejected')),
    -- 确保双语字段包含必需的键
    CONSTRAINT ai_tools_name_check CHECK (name ? 'en' AND name ? 'cn'),
    CONSTRAINT ai_tools_title_check CHECK (title ? 'en' AND title ? 'cn'),
    CONSTRAINT ai_tools_description_check CHECK (description ? 'en' AND description ? 'cn')
);

-- 创建索引
CREATE INDEX idx_ai_tools_category ON ai_tools(category);
CREATE INDEX idx_ai_tools_status ON ai_tools(status);
CREATE INDEX idx_ai_tools_featured ON ai_tools(featured);
CREATE INDEX idx_ai_tools_rating ON ai_tools(rating DESC);
CREATE INDEX idx_ai_tools_view_count ON ai_tools(view_count DESC);
CREATE INDEX idx_ai_tools_created_at ON ai_tools(created_at DESC);

-- JSONB字段索引
CREATE INDEX idx_ai_tools_name_gin ON ai_tools USING GIN(name);
CREATE INDEX idx_ai_tools_title_gin ON ai_tools USING GIN(title);
CREATE INDEX idx_ai_tools_description_gin ON ai_tools USING GIN(description);
CREATE INDEX idx_ai_tools_tags_gin ON ai_tools USING GIN(tags);
CREATE INDEX idx_ai_tools_industry_tags_gin ON ai_tools USING GIN(industry_tags);
CREATE INDEX idx_ai_tools_key_features_gin ON ai_tools USING GIN(key_features);

-- 双语搜索索引
CREATE INDEX idx_ai_tools_search_en ON ai_tools USING GIN(
    to_tsvector('english', (title->>'en') || ' ' || (description->>'en'))
);
CREATE INDEX idx_ai_tools_search_cn ON ai_tools USING GIN(
    to_tsvector('simple', (title->>'cn') || ' ' || (description->>'cn'))
);
```

### 2. categories - 分类表 (支持双语)

```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name JSONB NOT NULL,                           -- 分类名称 {"en": "image", "cn": "图像"}
    slug VARCHAR(100) UNIQUE NOT NULL,             -- URL标识符 (英文)
    display_name JSONB NOT NULL,                   -- 显示名称 {"en": "Image", "cn": "图像工具"}
    description JSONB,                             -- 分类描述 {"en": "...", "cn": "..."}
    icon VARCHAR(100),                             -- 图标名称
    color VARCHAR(7),                              -- 主题色
    
    -- 层级结构
    parent_id INTEGER REFERENCES categories(id),   -- 父分类ID
    level INTEGER DEFAULT 0,                       -- 层级深度
    sort_order INTEGER DEFAULT 0,                  -- 排序权重
    
    -- 统计信息
    tool_count INTEGER DEFAULT 0,                  -- 工具数量
    is_active BOOLEAN DEFAULT true,                -- 是否启用
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_active ON categories(is_active);
```

### 3. tags - 标签表 (支持双语)

```sql
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name JSONB NOT NULL,                           -- 标签名称 {"en": "chatbot", "cn": "聊天机器人"}
    slug VARCHAR(100) UNIQUE NOT NULL,             -- URL标识符 (英文)
    description JSONB,                             -- 标签描述 {"en": "...", "cn": "..."}
    category VARCHAR(50),                          -- 标签分类
    
    -- 统计信息
    usage_count INTEGER DEFAULT 0,                 -- 使用次数
    is_featured BOOLEAN DEFAULT false,             -- 是否推荐标签
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_tags_usage_count ON tags(usage_count DESC);
CREATE INDEX idx_tags_featured ON tags(is_featured);
```

### 4. tool_tags - 工具标签关联表

```sql
CREATE TABLE tool_tags (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    
    -- 关联权重
    relevance_score DECIMAL(3,2) DEFAULT 1.0,      -- 相关性评分
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE(tool_id, tag_id)
);

-- 创建索引
CREATE INDEX idx_tool_tags_tool_id ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag_id ON tool_tags(tag_id);
CREATE INDEX idx_tool_tags_relevance ON tool_tags(relevance_score DESC);
```

## 辅助表结构

### 5. user_favorites - 用户收藏表

```sql
CREATE TABLE user_favorites (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,                      -- 用户ID（暂时用整数，后续可扩展）
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    
    -- 收藏信息
    notes TEXT,                                    -- 用户备注
    folder_name VARCHAR(100),                      -- 收藏夹名称
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE(user_id, tool_id)
);

-- 创建索引
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_tool_id ON user_favorites(tool_id);
CREATE INDEX idx_user_favorites_created_at ON user_favorites(created_at DESC);
```

### 6. tool_reviews - 工具评价表

```sql
CREATE TABLE tool_reviews (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER NOT NULL REFERENCES ai_tools(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL,                      -- 用户ID
    
    -- 评价内容
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),                            -- 评价标题
    content TEXT,                                  -- 评价内容
    pros TEXT,                                     -- 优点
    cons TEXT,                                     -- 缺点
    
    -- 评价状态
    status VARCHAR(20) DEFAULT 'pending',          -- pending/approved/rejected
    is_verified BOOLEAN DEFAULT false,             -- 是否认证评价
    helpful_count INTEGER DEFAULT 0,               -- 有用数
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(tool_id, user_id)
);

-- 创建索引
CREATE INDEX idx_tool_reviews_tool_id ON tool_reviews(tool_id);
CREATE INDEX idx_tool_reviews_rating ON tool_reviews(rating DESC);
CREATE INDEX idx_tool_reviews_status ON tool_reviews(status);
CREATE INDEX idx_tool_reviews_created_at ON tool_reviews(created_at DESC);
```

### 7. crawl_logs - 爬取日志表

```sql
CREATE TABLE crawl_logs (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER REFERENCES ai_tools(id) ON DELETE SET NULL,
    
    -- 爬取信息
    source_url VARCHAR(500) NOT NULL,              -- 源URL
    crawl_type VARCHAR(50) NOT NULL,               -- 爬取类型
    status VARCHAR(20) NOT NULL,                   -- success/failed/partial
    
    -- 结果数据
    raw_data JSONB,                                -- 原始数据
    processed_data JSONB,                          -- 处理后数据
    error_message TEXT,                            -- 错误信息
    
    -- 统计信息
    processing_time INTEGER,                       -- 处理时间(毫秒)
    data_quality_score DECIMAL(3,2),               -- 数据质量评分
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_crawl_logs_tool_id ON crawl_logs(tool_id);
CREATE INDEX idx_crawl_logs_status ON crawl_logs(status);
CREATE INDEX idx_crawl_logs_created_at ON crawl_logs(created_at DESC);
CREATE INDEX idx_crawl_logs_source_url ON crawl_logs(source_url);
```

## 视图和触发器

### 1. 工具统计视图 (支持双语)

```sql
CREATE VIEW tool_stats AS
SELECT
    t.id,
    t.title,
    t.name,
    t.category,
    t.rating,
    t.view_count,
    t.page_screenshot,
    COUNT(DISTINCT r.id) as review_count,
    AVG(r.rating) as avg_rating,
    jsonb_array_length(COALESCE(t.tags, '[]'::jsonb)) as tag_count
FROM ai_tools t
LEFT JOIN tool_reviews r ON t.id = r.tool_id AND r.status = 'approved'
WHERE t.status = 'active'
GROUP BY t.id, t.title, t.name, t.category, t.rating, t.view_count, t.page_screenshot, t.tags;
```

### 2. 分类统计视图 (支持双语)

```sql
CREATE VIEW category_stats AS
SELECT
    c.id,
    c.name,
    c.display_name,
    c.slug,
    COUNT(t.id) as tool_count,
    AVG(t.rating) as avg_rating,
    SUM(t.view_count) as total_views
FROM categories c
LEFT JOIN ai_tools t ON c.slug = t.category AND t.status = 'active'
WHERE c.is_active = true
GROUP BY c.id, c.name, c.display_name, c.slug;
```

### 3. 自动更新触发器

```sql
-- 更新工具的updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_ai_tools_updated_at
    BEFORE UPDATE ON ai_tools
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 更新分类工具数量
CREATE OR REPLACE FUNCTION update_category_tool_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE categories SET tool_count = tool_count + 1
        WHERE slug = NEW.category;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.category != NEW.category THEN
            UPDATE categories SET tool_count = tool_count - 1
            WHERE slug = OLD.category;
            UPDATE categories SET tool_count = tool_count + 1
            WHERE slug = NEW.category;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE categories SET tool_count = tool_count - 1
        WHERE slug = OLD.category;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_category_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON ai_tools
    FOR EACH ROW EXECUTE FUNCTION update_category_tool_count();
```

## 数据库优化方案

### 1. 索引优化 (支持双语查询)

```sql
-- 复合索引优化查询性能
CREATE INDEX idx_ai_tools_category_rating ON ai_tools(category, rating DESC);
CREATE INDEX idx_ai_tools_status_featured ON ai_tools(status, featured DESC);

-- JSONB字段特定值索引
CREATE INDEX idx_ai_tools_pricing_type_en ON ai_tools((pricing_type->>'en'));
CREATE INDEX idx_ai_tools_pricing_type_cn ON ai_tools((pricing_type->>'cn'));

-- 双语全文搜索索引
CREATE INDEX idx_ai_tools_fulltext_en ON ai_tools
USING GIN(to_tsvector('english',
    (title->>'en') || ' ' || COALESCE((description->>'en'), '') || ' ' ||
    COALESCE(array_to_string(ARRAY(SELECT jsonb_array_elements_text(tags)), ' '), '')
));

CREATE INDEX idx_ai_tools_fulltext_cn ON ai_tools
USING GIN(to_tsvector('simple',
    (title->>'cn') || ' ' || COALESCE((description->>'cn'), '') || ' ' ||
    COALESCE(array_to_string(ARRAY(SELECT jsonb_array_elements_text(tags)), ' '), '')
));

-- 部分索引（只索引活跃工具）
CREATE INDEX idx_ai_tools_active_rating ON ai_tools(rating DESC)
WHERE status = 'active';
```

### 2. 分区策略

```sql
-- 按创建时间分区爬取日志表
CREATE TABLE crawl_logs_2024 PARTITION OF crawl_logs
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE crawl_logs_2025 PARTITION OF crawl_logs
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

### 3. 性能优化配置

```sql
-- 设置表的统计信息收集
ALTER TABLE ai_tools SET (autovacuum_analyze_scale_factor = 0.02);
ALTER TABLE tool_reviews SET (autovacuum_analyze_scale_factor = 0.05);

-- 设置填充因子优化更新性能
ALTER TABLE ai_tools SET (fillfactor = 90);
```

## 数据迁移脚本

### 1. 初始化分类数据 (17个分类，双语支持)

```sql
INSERT INTO categories (name, slug, display_name, description, icon, level, sort_order) VALUES
('{"en": "image", "cn": "图像"}', 'image', '{"en": "Image", "cn": "图像工具"}', '{"en": "AI image generation and editing tools", "cn": "AI图像生成和编辑工具"}', 'image', 0, 1),
('{"en": "video", "cn": "视频"}', 'video', '{"en": "Video", "cn": "视频工具"}', '{"en": "AI video generation and editing tools", "cn": "AI视频生成和编辑工具"}', 'video', 0, 2),
('{"en": "productivity", "cn": "效率"}', 'productivity', '{"en": "Productivity", "cn": "效率工具"}', '{"en": "AI productivity and automation tools", "cn": "AI效率和自动化工具"}', 'zap', 0, 3),
('{"en": "text&writing", "cn": "文本写作"}', 'text&writing', '{"en": "Text & Writing", "cn": "文本写作"}', '{"en": "AI writing and content creation tools", "cn": "AI写作和内容创作工具"}', 'edit', 0, 4),
('{"en": "business", "cn": "商业"}', 'business', '{"en": "Business", "cn": "商业工具"}', '{"en": "AI business and enterprise tools", "cn": "AI商业和企业工具"}', 'briefcase', 0, 5),
('{"en": "chatbot", "cn": "聊天机器人"}', 'chatbot', '{"en": "Chatbot", "cn": "聊天机器人"}', '{"en": "AI chatbot and conversation tools", "cn": "AI聊天机器人和对话工具"}', 'message-circle', 0, 6),
('{"en": "design&art", "cn": "设计艺术"}', 'design&art', '{"en": "Design & Art", "cn": "设计艺术"}', '{"en": "AI design and creative tools", "cn": "AI设计和创意工具"}', 'palette', 0, 7),
('{"en": "code&it", "cn": "编程IT"}', 'code&it', '{"en": "Code & IT", "cn": "编程IT"}', '{"en": "AI programming and development tools", "cn": "AI编程和开发工具"}', 'code', 0, 8),
('{"en": "marketing", "cn": "营销"}', 'marketing', '{"en": "Marketing", "cn": "营销工具"}', '{"en": "AI marketing and promotion tools", "cn": "AI营销和推广工具"}', 'trending-up', 0, 9),
('{"en": "prompt", "cn": "提示词"}', 'prompt', '{"en": "Prompt", "cn": "提示词"}', '{"en": "AI prompt engineering tools", "cn": "AI提示词工程工具"}', 'command', 0, 10),
('{"en": "voice", "cn": "语音"}', 'voice', '{"en": "Voice", "cn": "语音工具"}', '{"en": "AI voice and audio tools", "cn": "AI语音和音频工具"}', 'mic', 0, 11),
('{"en": "ai tools directory", "cn": "AI工具目录"}', 'ai-tools-directory', '{"en": "AI Tools Directory", "cn": "AI工具目录"}', '{"en": "AI tools directory and discovery platforms", "cn": "AI工具目录和发现平台"}', 'folder', 0, 12),
('{"en": "ai detector", "cn": "AI检测"}', 'ai-detector', '{"en": "AI Detector", "cn": "AI检测"}', '{"en": "AI detection and verification tools", "cn": "AI检测和验证工具"}', 'shield', 0, 13),
('{"en": "life assistant", "cn": "生活助手"}', 'life-assistant', '{"en": "Life Assistant", "cn": "生活助手"}', '{"en": "AI life and personal assistant tools", "cn": "AI生活和个人助手工具"}', 'user', 0, 14),
('{"en": "3d", "cn": "3D"}', '3d', '{"en": "3D", "cn": "3D工具"}', '{"en": "AI 3D modeling and design tools", "cn": "AI 3D建模和设计工具"}', 'box', 0, 15),
('{"en": "education", "cn": "教育"}', 'education', '{"en": "Education", "cn": "教育工具"}', '{"en": "AI education and learning tools", "cn": "AI教育和学习工具"}', 'book', 0, 16),
('{"en": "other", "cn": "其他"}', 'other', '{"en": "Other", "cn": "其他工具"}', '{"en": "Other AI tools and utilities", "cn": "其他AI工具和实用程序"}', 'more-horizontal', 0, 17);
```

### 2. 初始化常用标签 (双语支持)

```sql
INSERT INTO tags (name, slug, description, category, is_featured) VALUES
('{"en": "chatbot", "cn": "聊天机器人"}', 'chatbot', '{"en": "Chatbot functionality", "cn": "聊天机器人功能"}', 'function', true),
('{"en": "writing", "cn": "写作"}', 'writing', '{"en": "Writing assistance", "cn": "写作辅助"}', 'function', true),
('{"en": "image generation", "cn": "图像生成"}', 'image-generation', '{"en": "Image generation", "cn": "图像生成"}', 'function', true),
('{"en": "code assistant", "cn": "编程助手"}', 'code-assistant', '{"en": "Programming assistant", "cn": "编程助手"}', 'function', true),
('{"en": "free", "cn": "免费"}', 'free', '{"en": "Free to use", "cn": "免费使用"}', 'pricing', true),
('{"en": "api", "cn": "API接口"}', 'api', '{"en": "Provides API interface", "cn": "提供API接口"}', 'feature', true),
('{"en": "no-code", "cn": "无代码"}', 'no-code', '{"en": "No coding required", "cn": "无需编程"}', 'feature', true),
('{"en": "real-time", "cn": "实时"}', 'real-time', '{"en": "Real-time processing", "cn": "实时处理"}', 'feature', false),
('{"en": "collaboration", "cn": "协作"}', 'collaboration', '{"en": "Team collaboration", "cn": "团队协作"}', 'feature', false),
('{"en": "enterprise", "cn": "企业级"}', 'enterprise', '{"en": "Enterprise grade", "cn": "企业级"}', 'target', false);
```

### 3. 数据导入示例

```sql
-- 从标准化JSON数据导入工具示例
INSERT INTO ai_tools (
    name, title, slug, url, page_screenshot,
    description, long_description, key_features,
    use_cases, target_audience, category, subcategory,
    tags, industry_tags, pricing_type, pricing_details,
    trial_available, rating, view_count, status, meta_info
) VALUES (
    '{"en": "veo-5-ai", "cn": "Veo 5 AI"}',
    '{"en": "Veo 5 AI Video Generator", "cn": "Veo 5 AI 视频生成器"}',
    'veo-5-ai',
    'https://veo5.org',
    'screenshots/toolify/Veo_5_Ai.jpg',
    '{"en": "Advanced AI video generation platform", "cn": "先进的AI视频生成平台"}',
    '{"en": "Veo 5 is an advanced AI video generation platform...", "cn": "Veo 5是一个先进的AI视频生成平台..."}',
    '[
        {"en": "Fast video generation", "cn": "快速视频生成"},
        {"en": "Realistic sound and lip sync", "cn": "真实声音和唇形同步"}
    ]',
    '{"en": "Marketing campaigns, education", "cn": "营销广告、教育培训"}',
    '{"en": "Content creators, marketers", "cn": "内容创作者、营销人员"}',
    'video',
    '{"en": "video-generation", "cn": "视频生成"}',
    '[
        {"en": "AI video generation", "cn": "AI视频生成"},
        {"en": "text to video", "cn": "文本转视频"}
    ]',
    '[
        {"en": "marketing", "cn": "营销"},
        {"en": "education", "cn": "教育"}
    ]',
    '{"en": "free", "cn": "免费"}',
    '{"free_plan": {"en": "Free Forever", "cn": "永久免费"}}',
    '{"en": "yes", "cn": "是"}',
    0,
    50000,
    'active',
    '{"source_site": "toolify", "processing_timestamp": "2025-01-24T21:23:25.929655"}'
);
```

## 数据备份和恢复

### 1. 备份策略

```bash
# 每日全量备份
pg_dump -h localhost -U username -d lookaitools > backup_$(date +%Y%m%d).sql

# 增量备份（WAL归档）
archive_command = 'cp %p /backup/wal/%f'
```

### 2. 恢复脚本

```bash
# 恢复数据库
psql -h localhost -U username -d lookaitools < backup_20250124.sql

# 恢复到指定时间点
pg_basebackup -h localhost -D /backup/base -U username -v -P -W
```

---

*文档创建时间: 2025-01-24*
*版本: v2.0*
*状态: 数据库设计完成 - 支持双语内容和17个分类*
*更新内容: 添加双语JSONB字段支持、17个AI工具分类、page_screenshot字段*
